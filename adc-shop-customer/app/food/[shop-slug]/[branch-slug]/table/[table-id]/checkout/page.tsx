'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Users, MapPin, CreditCard, Clock } from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { toast } from 'sonner';

interface TableCheckoutPageProps {}

export default function TableCheckoutPage({}: TableCheckoutPageProps) {
  const params = useParams();
  const router = useRouter();
  const shopSlug = params['shop-slug'] as string;
  const branchSlug = params['branch-slug'] as string;
  const tableId = params['table-id'] as string;
  
  const [tableInfo, setTableInfo] = useState<any>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: '',
    specialRequests: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { cart, getCartTotal, clearCart } = useCart();

  useEffect(() => {
    // Get table context from localStorage
    const storedTableContext = localStorage.getItem('tableContext');
    if (storedTableContext) {
      setTableInfo(JSON.parse(storedTableContext));
    }
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const goBackToCart = () => {
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/cart`);
  };

  const handleSubmitOrder = async () => {
    if (cart.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    if (!customerInfo.name.trim()) {
      toast.error('Please enter your name');
      return;
    }

    if (!customerInfo.phone.trim()) {
      toast.error('Please enter your phone number');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare order data
      const orderData = {
        tableId,
        shopSlug,
        branchSlug,
        tableInfo,
        customerInfo,
        items: cart,
        totals: {
          subtotal: getCartTotal(),
          serviceCharge: getCartTotal() * 0.10,
          tax: getCartTotal() * 0.07,
          total: getCartTotal() * 1.17, // subtotal + service charge + tax
        },
        orderType: 'table_order',
        timestamp: new Date().toISOString(),
      };

      // Here you would typically send the order to your backend
      // For now, we'll simulate the order submission
      console.log('Submitting order:', orderData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear cart and redirect to success page
      clearCart();
      toast.success('Order submitted successfully!');
      
      // Redirect to order confirmation page
      router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/order-confirmation`);
      
    } catch (error) {
      console.error('Failed to submit order:', error);
      toast.error('Failed to submit order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const subtotal = getCartTotal();
  const serviceCharge = subtotal * 0.10;
  const tax = subtotal * 0.07;
  const total = subtotal + serviceCharge + tax;

  if (cart.length === 0) {
    return (
      <div className="min-h-screen bg-[#f4f2f0] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">Add some items to your cart before checkout.</p>
          <Button 
            onClick={() => router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/order`)}
            className="bg-[#e58219] hover:bg-[#d4751a] text-white"
          >
            Browse Menu
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f4f2f0]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={goBackToCart}
              className="text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Cart
            </Button>
            
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800">Checkout</h1>
              {tableInfo && (
                <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    <span>{tableInfo.shopSlug} - {tableInfo.branchSlug}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{tableInfo.tableName}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Table Info Banner */}
      {tableInfo && (
        <div className="bg-[#e5ccb2] border-b">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <div className="flex items-center justify-center gap-2 text-[#8b4513]">
              <Users className="w-5 h-5" />
              <span className="font-medium">Finalizing order for {tableInfo.tableName}</span>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Customer Information Form */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      value={customerInfo.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter your name"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={customerInfo.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Enter your phone number"
                      className="mt-1"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email">Email (Optional)</Label>
                  <Input
                    id="email"
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter your email"
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="special-requests">Special Requests (Optional)</Label>
                  <Textarea
                    id="special-requests"
                    value={customerInfo.specialRequests}
                    onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                    placeholder="Any special dietary requirements or requests..."
                    className="mt-1"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Order Summary for Mobile */}
            <Card className="lg:hidden">
              <CardHeader>
                <CardTitle className="text-lg">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal ({cart.length} items)</span>
                    <span>฿{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service Charge (10%)</span>
                    <span>฿{serviceCharge.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>VAT (7%)</span>
                    <span>฿{tax.toFixed(2)}</span>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span className="text-[#e58219]">฿{total.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary - Desktop */}
          <div className="lg:col-span-1 hidden lg:block">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="text-lg">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal ({cart.length} items)</span>
                    <span>฿{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service Charge (10%)</span>
                    <span>฿{serviceCharge.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>VAT (7%)</span>
                    <span>฿{tax.toFixed(2)}</span>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span className="text-[#e58219]">฿{total.toFixed(2)}</span>
                </div>
                
                <div className="pt-4 space-y-3">
                  <Button 
                    onClick={handleSubmitOrder}
                    disabled={isSubmitting}
                    className="w-full bg-[#e58219] hover:bg-[#d4751a] text-white py-3 text-lg font-semibold"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Submitting Order...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <CreditCard className="w-5 h-5" />
                        Submit Order
                      </div>
                    )}
                  </Button>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600 justify-center">
                    <Clock className="w-4 h-4" />
                    <span>Estimated preparation time: 15-20 minutes</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button for Mobile */}
        <div className="lg:hidden mt-6">
          <Button 
            onClick={handleSubmitOrder}
            disabled={isSubmitting}
            className="w-full bg-[#e58219] hover:bg-[#d4751a] text-white py-3 text-lg font-semibold"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Submitting Order...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Submit Order - ฿{total.toFixed(2)}
              </div>
            )}
          </Button>
          
          <div className="flex items-center gap-2 text-sm text-gray-600 justify-center mt-3">
            <Clock className="w-4 h-4" />
            <span>Estimated preparation time: 15-20 minutes</span>
          </div>
        </div>
      </div>
    </div>
  );
}
