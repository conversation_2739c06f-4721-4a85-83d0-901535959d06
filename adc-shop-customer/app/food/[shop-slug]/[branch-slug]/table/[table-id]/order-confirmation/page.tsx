'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Users, MapPin, Phone, Mail } from 'lucide-react';

interface OrderConfirmationPageProps {}

export default function OrderConfirmationPage({}: OrderConfirmationPageProps) {
  const params = useParams();
  const router = useRouter();
  const shopSlug = params['shop-slug'] as string;
  const branchSlug = params['branch-slug'] as string;
  const tableId = params['table-id'] as string;
  
  const [orderInfo, setOrderInfo] = useState<any>(null);

  useEffect(() => {
    // In a real implementation, you would fetch the order details from the backend
    // For now, we'll create a mock order
    const mockOrder = {
      id: `ORD-${Date.now()}`,
      tableId,
      shopSlug,
      branchSlug,
      tableName: `Table ${tableId.slice(-4)}`,
      status: 'confirmed',
      estimatedTime: '15-20 minutes',
      customerInfo: {
        name: 'Customer',
        phone: '+66 XX XXX XXXX',
        email: '<EMAIL>',
      },
      items: [
        { name: 'Pad Thai', quantity: 2, price: 120 },
        { name: 'Tom Yum Soup', quantity: 1, price: 80 },
      ],
      totals: {
        subtotal: 320,
        serviceCharge: 32,
        tax: 22.4,
        total: 374.4,
      },
      timestamp: new Date().toISOString(),
    };
    
    setOrderInfo(mockOrder);
  }, [tableId, shopSlug, branchSlug]);

  const handleNewOrder = () => {
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/order`);
  };

  const handleViewOrders = () => {
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/orders`);
  };

  if (!orderInfo) {
    return (
      <div className="min-h-screen bg-[#f4f2f0] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#e58219]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f4f2f0]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800">Order Confirmed!</h1>
            <p className="text-gray-600 mt-1">Your order has been successfully submitted</p>
          </div>
        </div>
      </div>

      {/* Success Banner */}
      <div className="bg-green-50 border-b border-green-200">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center gap-3 text-green-700">
            <CheckCircle className="w-8 h-8" />
            <div className="text-center">
              <h2 className="text-lg font-semibold">Order #{orderInfo.id}</h2>
              <p className="text-sm">Estimated preparation time: {orderInfo.estimatedTime}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-[#e58219]" />
                  Order Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    {orderInfo.status.charAt(0).toUpperCase() + orderInfo.status.slice(1)}
                  </Badge>
                  <span className="text-gray-600">
                    Your order is being prepared by the kitchen
                  </span>
                </div>
                
                <div className="mt-4 p-4 bg-[#f4f2f1] rounded-lg">
                  <div className="flex items-center gap-2 text-[#8b4513]">
                    <Clock className="w-4 h-4" />
                    <span className="font-medium">Estimated ready time: {orderInfo.estimatedTime}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    We'll notify the restaurant staff when your order is ready
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Table Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-[#e58219]" />
                  Table Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span>{orderInfo.shopSlug} - {orderInfo.branchSlug}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-gray-500" />
                    <span>{orderInfo.tableName}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Name:</span>
                    <span>{orderInfo.customerInfo.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span>{orderInfo.customerInfo.phone}</span>
                  </div>
                  {orderInfo.customerInfo.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span>{orderInfo.customerInfo.email}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Order Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {orderInfo.items.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <div>
                        <span className="font-medium">{item.name}</span>
                        <span className="text-gray-500 ml-2">x{item.quantity}</span>
                      </div>
                      <span className="font-medium">฿{(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>฿{orderInfo.totals.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service Charge (10%)</span>
                    <span>฿{orderInfo.totals.serviceCharge.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>VAT (7%)</span>
                    <span>฿{orderInfo.totals.tax.toFixed(2)}</span>
                  </div>
                </div>
                
                <hr />
                
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span className="text-[#e58219]">฿{orderInfo.totals.total.toFixed(2)}</span>
                </div>
                
                <div className="pt-4 space-y-3">
                  <Button 
                    onClick={handleNewOrder}
                    className="w-full bg-[#e58219] hover:bg-[#d4751a] text-white"
                  >
                    Order More Items
                  </Button>
                  
                  <Button 
                    onClick={handleViewOrders}
                    variant="outline"
                    className="w-full"
                  >
                    View All Orders
                  </Button>
                </div>
                
                <div className="pt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Order placed at {new Date(orderInfo.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
