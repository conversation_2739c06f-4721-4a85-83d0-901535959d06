'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Plus, Minus, Trash2, Users, MapPin } from 'lucide-react';
import Image from 'next/image';
import { useCart } from '@/hooks/useCart';
import { toast } from 'sonner';

interface TableCartPageProps {}

export default function TableCartPage({}: TableCartPageProps) {
  const params = useParams();
  const router = useRouter();
  const shopSlug = params['shop-slug'] as string;
  const branchSlug = params['branch-slug'] as string;
  const tableId = params['table-id'] as string;
  
  const [tableInfo, setTableInfo] = useState<any>(null);
  const { cart, updateQuantity, removeFromCart, getCartTotal, getCartItemCount, clearCart } = useCart();

  useEffect(() => {
    // Get table context from localStorage
    const storedTableContext = localStorage.getItem('tableContext');
    if (storedTableContext) {
      setTableInfo(JSON.parse(storedTableContext));
    }
  }, []);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      toast.success('Item removed from cart');
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleRemoveItem = (itemId: string) => {
    removeFromCart(itemId);
    toast.success('Item removed from cart');
  };

  const goBackToMenu = () => {
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/order`);
  };

  const proceedToPayment = () => {
    if (cart.length === 0) {
      toast.error('Your cart is empty');
      return;
    }
    
    // Navigate to payment/checkout page
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/checkout`);
  };

  const subtotal = getCartTotal();
  const tax = subtotal * 0.07; // 7% VAT
  const serviceCharge = subtotal * 0.10; // 10% service charge
  const total = subtotal + tax + serviceCharge;

  return (
    <div className="min-h-screen bg-[#f4f2f0]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={goBackToMenu}
              className="text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Menu
            </Button>
            
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800">Your Order</h1>
              {tableInfo && (
                <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    <span>{tableInfo.shopSlug} - {tableInfo.branchSlug}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{tableInfo.tableName}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Table Info Banner */}
      {tableInfo && (
        <div className="bg-[#e5ccb2] border-b">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <div className="flex items-center justify-center gap-2 text-[#8b4513]">
              <Users className="w-5 h-5" />
              <span className="font-medium">Order for {tableInfo.tableName}</span>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-6">
        {cart.length === 0 ? (
          /* Empty Cart State */
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15.5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Add some delicious items from the menu to get started!</p>
            <Button 
              onClick={goBackToMenu}
              className="bg-[#e58219] hover:bg-[#d4751a] text-white"
            >
              Browse Menu
            </Button>
          </div>
        ) : (
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Cart Items ({getCartItemCount()})
              </h2>
              
              {cart.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      {item.image && (
                        <div className="w-20 h-20 relative flex-shrink-0 rounded-lg overflow-hidden">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-gray-800">{item.name}</h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        {item.description && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {item.description}
                          </p>
                        )}
                        
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="w-8 h-8 p-0"
                            >
                              <Minus className="w-3 h-3" />
                            </Button>
                            
                            <span className="font-medium text-gray-800 min-w-[2rem] text-center">
                              {item.quantity}
                            </span>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              className="w-8 h-8 p-0"
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </div>
                          
                          <div className="text-right">
                            <div className="text-lg font-bold text-[#e58219]">
                              ฿{(item.price * item.quantity).toFixed(2)}
                            </div>
                            <div className="text-sm text-gray-500">
                              ฿{item.price} each
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle className="text-lg">Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal</span>
                      <span>฿{subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Service Charge (10%)</span>
                      <span>฿{serviceCharge.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>VAT (7%)</span>
                      <span>฿{tax.toFixed(2)}</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span className="text-[#e58219]">฿{total.toFixed(2)}</span>
                  </div>
                  
                  <Button 
                    onClick={proceedToPayment}
                    className="w-full bg-[#e58219] hover:bg-[#d4751a] text-white py-3 text-lg font-semibold"
                  >
                    Proceed to Payment
                  </Button>
                  
                  <Button 
                    onClick={goBackToMenu}
                    variant="outline"
                    className="w-full"
                  >
                    Add More Items
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
