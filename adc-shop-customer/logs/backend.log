Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-04T14:29:04+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-04T14:29:04+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-04T14:29:04+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func3 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-04T14:29:04+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:13 +07] \"GET /cart HTTP/1.1 200 74.334µs \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:13+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: main, page=1, limit=20","time":"2025-06-04T14:29:13+07:00"}

2025/06/04 14:29:13 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[71.244ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'main' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:29:13+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:13 +07] \"GET /shops/slug/thai-delight/branches/slug/main/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 71.8235ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:13+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:21 +07] \"GET /cart HTTP/1.1 200 22.417µs \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:21+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: main, page=1, limit=20","time":"2025-06-04T14:29:21+07:00"}

2025/06/04 14:29:21 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[74.012ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'main' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:29:21+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:21 +07] \"GET /shops/slug/thai-delight/branches/slug/main/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 74.361541ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:21+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-04T14:29:29+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-04T14:29:29+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-04T14:29:29+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T14:29:29+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-04T14:29:29+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:29 +07] \"GET /shops/filter-options HTTP/1.1 200 289.759ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:29+07:00"}

2025/06/04 14:29:29 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:140 [33mSLOW SQL >= 200ms
[0m[31;1m[272.632ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:29 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 374.570459ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:29+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: downtown, page=1, limit=20","time":"2025-06-04T14:29:32+07:00"}

2025/06/04 14:29:32 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[68.889ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:29:32+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:29:32 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 69.249584ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:29:32+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-04T14:31:22+07:00"}

2025/06/04 14:31:22 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[65.880ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:31:22+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:31:22 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 66.167916ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:31:22+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-04T14:32:34+07:00"}

2025/06/04 14:32:34 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[65.363ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:32:34+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:32:34 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 65.621417ms \"curl/8.7.1\" \"\n","time":"2025-06-04T14:32:34+07:00"}
{"all_query_params":{},"level":"info","msg":"Received query parameters","time":"2025-06-04T14:32:41+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-04T14:32:41+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"","search":"","time":"2025-06-04T14:32:41+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T14:32:41+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-04T14:32:41+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:32:41 +07] \"GET /shops HTTP/1.1 200 107.940208ms \"curl/8.7.1\" \"\n","time":"2025-06-04T14:32:41+07:00"}
{"level":"info","msg":"Getting shop settings for shop slug: thai-delight","time":"2025-06-04T14:32:48+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:32:48 +07] \"GET /shops/slug/thai-delight HTTP/1.1 200 106.247584ms \"curl/8.7.1\" \"\n","time":"2025-06-04T14:32:48+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-04T14:34:52+07:00"}

2025/06/04 14:34:52 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:478 [35;1mERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)
[0m[33m[65.064ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."address_latitude","shop_branches"."address_longitude","shop_branches"."address_location_accuracy","shop_branches"."address_geocoded_at","shop_branches"."address_place_id","shop_branches"."address_formatted_address","shop_branches"."address_location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: failed to get branch: ERROR: column shop_branches.address_latitude does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-04T14:34:52+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:34:52 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 65.3625ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-04T14:34:52+07:00"}
{"level":"info","msg":"Getting shop settings for shop slug: thai-delight","time":"2025-06-04T14:36:23+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 14:36:23 +07] \"GET /shops/slug/thai-delight HTTP/1.1 200 69.19525ms \"curl/8.7.1\" \"\n","time":"2025-06-04T14:36:23+07:00"}
{"level":"info","msg":"Shutting down server...","time":"2025-06-04T14:37:07+07:00"}
{"level":"info","msg":"Customer API exited","time":"2025-06-04T14:37:07+07:00"}
