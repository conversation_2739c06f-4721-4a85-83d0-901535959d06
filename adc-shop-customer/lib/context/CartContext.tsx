"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { cartService } from '@/lib/services/cartService';

export interface CartItem {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  quantity: number;
  // Shop and branch context for proper order routing
  shopSlug?: string;
  branchSlug?: string;
}

interface CartContextType {
  cartItems: CartItem[];
  isLoading: boolean;
  error: string | null;
  addToCart: (item: Omit<CartItem, 'quantity'>) => Promise<void>;
  updateQuantity: (id: string, quantity: number) => Promise<void>;
  removeFromCart: (id: string) => Promise<void>;
  clearCart: () => Promise<void>;
  clearBranchCart: (shopSlug: string, branchSlug: string) => Promise<void>;
  getBranchCartItems: (shopSlug: string, branchSlug: string) => CartItem[];
  getTotalItems: () => number;
  getTotalPrice: () => number;
  getBranchTotalItems: (shopSlug: string, branchSlug: string) => number;
  getBranchTotalPrice: (shopSlug: string, branchSlug: string) => number;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status } = useSession();

  // Load cart from backend on mount and when session changes
  useEffect(() => {
    loadCart();
  }, []);

  // Sync cart when user logs in
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      syncCartOnLogin();
    }
  }, [status, session]);

  const loadCart = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await cartService.getCart();

      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      } else {
        setError(response.message || 'Failed to load cart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load cart');
    } finally {
      setIsLoading(false);
    }
  };

  const syncCartOnLogin = async () => {
    try {
      const response = await cartService.syncCartOnLogin();
      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      }
    } catch (err) {
      console.error('Failed to sync cart on login:', err);
    }
  };

  const addToCart = async (item: Omit<CartItem, 'quantity'>) => {
    try {
      setError(null);
      const response = await cartService.addToCart(item);

      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      } else {
        setError(response.message || 'Failed to add item to cart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add item to cart');
    }
  };

  const updateQuantity = async (id: string, quantity: number) => {
    try {
      setError(null);
      if (quantity === 0) {
        await removeFromCart(id);
        return;
      }

      const response = await cartService.updateQuantity(id, quantity);

      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      } else {
        setError(response.message || 'Failed to update quantity');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update quantity');
    }
  };

  const removeFromCart = async (id: string) => {
    try {
      setError(null);
      const response = await cartService.removeFromCart(id);

      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      } else {
        setError(response.message || 'Failed to remove item');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove item');
    }
  };

  const clearCart = async () => {
    try {
      setError(null);
      const response = await cartService.clearCart();

      if (response.success) {
        setCartItems([]);
      } else {
        setError(response.message || 'Failed to clear cart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear cart');
    }
  };

  const clearBranchCart = async (shopSlug: string, branchSlug: string) => {
    try {
      setError(null);
      const response = await cartService.clearBranchCart(shopSlug, branchSlug);

      if (response.success && response.data) {
        setCartItems(response.data.items || []);
      } else {
        setError(response.message || 'Failed to clear branch cart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear branch cart');
    }
  };

  const getBranchCartItems = (shopSlug: string, branchSlug: string) => {
    return cartItems.filter(item =>
      item.shopSlug === shopSlug && item.branchSlug === branchSlug
    );
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getBranchTotalItems = (shopSlug: string, branchSlug: string) => {
    const branchItems = getBranchCartItems(shopSlug, branchSlug);
    return branchItems.reduce((total, item) => total + item.quantity, 0);
  };

  const getBranchTotalPrice = (shopSlug: string, branchSlug: string) => {
    const branchItems = getBranchCartItems(shopSlug, branchSlug);
    return branchItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const refreshCart = async () => {
    await loadCart();
  };

  const value = {
    cartItems,
    isLoading,
    error,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    clearBranchCart,
    getBranchCartItems,
    getTotalItems,
    getTotalPrice,
    getBranchTotalItems,
    getBranchTotalPrice,
    refreshCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
