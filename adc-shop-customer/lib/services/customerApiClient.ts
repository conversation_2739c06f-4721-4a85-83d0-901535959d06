// Customer API Client for integrating with the Golang backend

export interface PaginationInfo {
  current_page: number;
  total_pages: number;
  total_items: number;
  items_per_page: number;
  has_next: boolean;
  has_prev: boolean;
  center_pages?: number[];
  show_first?: boolean;
  show_last?: boolean;
  next_cursor?: string;
  prev_cursor?: string;
}

export interface ResponseMeta {
  request_id?: string;
  timestamp: string;
  version?: string;
  process_time?: string;
}

export interface CustomerResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  pagination?: PaginationInfo;
  meta?: ResponseMeta;
}

export interface CustomerShopSettings {
  id: string;
  slug: string;
  name: string;
  description: string;
  logo: string;
  cover_image: string;
  cuisine_type: string;
  price_range: string;
  rating: number;
  review_count: number;
  phone: string;
  email: string;
  website: string;
  address: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    latitude?: number;
    longitude?: number;
  };
  business_hours: Record<string, string>;
  timezone: string;
  theme: {
    primary_color: string;
    secondary_color: string;
    accent_color: string;
    background_color?: string;
    text_color?: string;
    font_family?: string;
    logo_position?: string;
  };
  features: {
    online_ordering: boolean;
    table_reservations: boolean;
    qr_menu: boolean;
    reviews: boolean;
    delivery_enabled: boolean;
    pickup_enabled: boolean;
    loyalty_program?: boolean;
    gift_cards?: boolean;
  };
  payment_methods: string[];
  currency: string;
  tax_rate: number;
  service_charge_rate: number;
  default_tip_percentage: number;
  social_media: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    tiktok?: string;
    youtube?: string;
  };
  is_open: boolean;
  is_active: boolean;
  updated_at: string;
}

export interface CustomerMenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  is_available: boolean;
  is_popular: boolean;
  is_new: boolean;
  is_vegetarian: boolean;
  is_vegan: boolean;
  is_gluten_free: boolean;
  is_spicy: boolean;
  spicy_level: number;
  calories?: number;
  preparation_time?: number;
  tags: string[];
  allergens: string[];
  has_customizations: boolean;
  customizations?: MenuItemCustomization[];
  rating: number;
  review_count: number;
  created_at: string;
  updated_at: string;
}

export interface MenuItemCustomization {
  id: string;
  name: string;
  type: string;
  required: boolean;
  max_selections?: number;
  options?: MenuItemCustomizationOption[];
}

export interface MenuItemCustomizationOption {
  id: string;
  name: string;
  price_change: number;
  is_available: boolean;
}

export interface MenuCategory {
  id: string;
  name: string;
  description: string;
  image?: string;
  sort_order: number;
  is_active: boolean;
  item_count: number;
}

export interface ShopFilters {
  page?: number;
  limit?: number;
  search?: string;
  cuisine_type?: string[];
  price_range?: string[];
  min_rating?: number;
  is_open?: boolean;
  has_delivery?: boolean;
  has_pickup?: boolean;
  latitude?: number;
  longitude?: number;
  radius?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface MenuFilters {
  page?: number;
  limit?: number;
  search?: string;
  category_id?: string;
  categories?: string[];
  is_available?: boolean;
  is_popular?: boolean;
  is_new?: boolean;
  is_vegetarian?: boolean;
  is_vegan?: boolean;
  is_gluten_free?: boolean;
  is_spicy?: boolean;
  max_spicy_level?: number;
  price_min?: number;
  price_max?: number;
  max_calories?: number;
  max_prep_time?: number;
  min_rating?: number;
  allergens?: string[];
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

class CustomerApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8081') {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<CustomerResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, item.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });

    return searchParams.toString();
  }

  // Shop API methods
  async getShops(filters: ShopFilters = {}): Promise<CustomerResponse<{ shops: CustomerShopSettings[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops${queryString ? `?${queryString}` : ''}`);
  }

  async getShop(shopId: string): Promise<CustomerResponse<{ shop: CustomerShopSettings }>> {
    return this.request(`/api/v1/shops/${shopId}`);
  }

  async searchShops(query: string, filters: ShopFilters = {}): Promise<CustomerResponse<{ shops: CustomerShopSettings[] }>> {
    const queryString = this.buildQueryString({ q: query, ...filters });
    return this.request(`/api/v1/shops/search?${queryString}`);
  }

  async getPopularShops(filters: ShopFilters = {}): Promise<CustomerResponse<{ shops: CustomerShopSettings[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/popular${queryString ? `?${queryString}` : ''}`);
  }

  async getNearbyShops(
    latitude: number,
    longitude: number,
    radius: number = 5,
    filters: ShopFilters = {}
  ): Promise<CustomerResponse<{ shops: CustomerShopSettings[] }>> {
    const queryString = this.buildQueryString({ latitude, longitude, radius, ...filters });
    return this.request(`/api/v1/shops/nearby?${queryString}`);
  }

  async getShopsByCategory(category: string, filters: ShopFilters = {}): Promise<CustomerResponse<{ shops: CustomerShopSettings[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/category/${category}${queryString ? `?${queryString}` : ''}`);
  }

  async getShopStatus(shopId: string): Promise<CustomerResponse<{ is_open: boolean }>> {
    return this.request(`/api/v1/shops/${shopId}/status`);
  }

  // Menu API methods
  async getMenuItems(
    shopId: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/${shopId}/menu${queryString ? `?${queryString}` : ''}`);
  }

  async getMenuItem(itemId: string): Promise<CustomerResponse<{ item: CustomerMenuItem }>> {
    return this.request(`/api/v1/menu/items/${itemId}`);
  }

  async getPopularMenuItems(
    shopId: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/${shopId}/menu/popular${queryString ? `?${queryString}` : ''}`);
  }

  async getNewMenuItems(
    shopId: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/${shopId}/menu/new${queryString ? `?${queryString}` : ''}`);
  }

  async getVegetarianItems(
    shopId: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/${shopId}/menu/vegetarian${queryString ? `?${queryString}` : ''}`);
  }

  async searchMenuItems(
    shopId: string,
    query: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[] }>> {
    const queryString = this.buildQueryString({ q: query, ...filters });
    return this.request(`/api/v1/shops/${shopId}/menu/search?${queryString}`);
  }

  async getMenuCategories(shopId: string): Promise<CustomerResponse<{ categories: MenuCategory[] }>> {
    return this.request(`/api/v1/shops/${shopId}/menu/categories`);
  }

  async getItemsByCategory(
    shopId: string,
    categoryId: string,
    filters: MenuFilters = {}
  ): Promise<CustomerResponse<{ items: CustomerMenuItem[] }>> {
    const queryString = this.buildQueryString(filters);
    return this.request(`/api/v1/shops/${shopId}/menu/categories/${categoryId}${queryString ? `?${queryString}` : ''}`);
  }
}

// Export singleton instance
export const customerApi = new CustomerApiClient();
export default CustomerApiClient;
