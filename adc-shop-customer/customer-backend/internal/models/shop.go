package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Shop represents a business entity (matches merchant backend structure for customer queries)
type Shop struct {
	BaseModel
	OwnerID     uuid.UUID `json:"owner_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug        string    `json:"slug" gorm:"type:varchar(255);uniqueIndex;not null"`
	Description string    `json:"description" gorm:"type:text"`
	ShopType    string    `json:"shop_type" gorm:"type:varchar(50);not null"` // restaurant, cafe, bakery, etc.
	Email       string    `json:"email" gorm:"type:varchar(255)"`
	Phone       string    `json:"phone" gorm:"type:varchar(50)"`
	Website     string    `json:"website" gorm:"type:varchar(255)"`
	Logo        string    `json:"logo" gorm:"type:varchar(500)"`
	CoverImage  string    `json:"cover_image" gorm:"type:varchar(500)"`
	Address     Address   `json:"address" gorm:"embedded;embeddedPrefix:address_"`

	// Location fields (direct mapping to database columns)
	Latitude         *float64   `json:"latitude" gorm:"type:decimal(10,8)"`
	Longitude        *float64   `json:"longitude" gorm:"type:decimal(11,8)"`
	LocationAccuracy *int       `json:"location_accuracy" gorm:"default:0"`
	GeocodedAt       *time.Time `json:"geocoded_at"`
	PlaceID          string     `json:"place_id" gorm:"type:varchar(255)"`
	FormattedAddress string     `json:"formatted_address" gorm:"type:text"`
	LocationType     string     `json:"location_type" gorm:"type:varchar(50);default:'manual'"` // manual, geocoded, gps, imported

	// Business details
	CuisineType string  `json:"cuisine_type" gorm:"type:varchar(100)"`
	PriceRange  string  `json:"price_range" gorm:"type:varchar(20)"` // $, $$, $$$, $$$$
	Rating      float64 `json:"rating" gorm:"type:decimal(3,2);default:0"`
	ReviewCount int     `json:"review_count" gorm:"default:0"`

	// Social media
	SocialMedia SocialMediaLinks `json:"social_media" gorm:"type:jsonb"`

	// Business settings
	Settings      ShopSettings  `json:"settings" gorm:"type:jsonb"`
	BusinessHours BusinessHours `json:"business_hours" gorm:"type:jsonb"`

	// Status
	Status     string `json:"status" gorm:"type:varchar(20);default:'active'"` // active, inactive, suspended
	IsVerified bool   `json:"is_verified" gorm:"default:false"`
	IsActive   bool   `json:"is_active" gorm:"default:true"`
}

// ShopBranch represents a branch/location of a shop (matches merchant backend)
type ShopBranch struct {
	BaseModel
	ShopID uuid.UUID `json:"shop_id" gorm:"type:uuid;not null;index"`
	Name   string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug   string    `json:"slug" gorm:"type:varchar(255);not null"`
	Email  string    `json:"email" gorm:"type:varchar(255)"`
	Phone  string    `json:"phone" gorm:"type:varchar(50)"`

	// Address fields (with address_ prefix for basic address info)
	AddressStreet  string `json:"address_street" gorm:"column:address_street"`
	AddressCity    string `json:"address_city" gorm:"column:address_city"`
	AddressState   string `json:"address_state" gorm:"column:address_state"`
	AddressZipCode string `json:"address_zip_code" gorm:"column:address_zip_code"`
	AddressCountry string `json:"address_country" gorm:"column:address_country"`

	// Location fields (without prefix, directly on the table)
	Latitude         *float64   `json:"latitude" gorm:"column:latitude"`
	Longitude        *float64   `json:"longitude" gorm:"column:longitude"`
	LocationAccuracy *int       `json:"location_accuracy" gorm:"column:location_accuracy"`
	GeocodedAt       *time.Time `json:"geocoded_at" gorm:"column:geocoded_at"`
	PlaceID          string     `json:"place_id" gorm:"column:place_id"`
	FormattedAddress string     `json:"formatted_address" gorm:"column:formatted_address"`
	LocationType     string     `json:"location_type" gorm:"column:location_type"`

	// Branch-specific settings
	Settings      BranchSettings `json:"settings" gorm:"type:jsonb"`
	BusinessHours BusinessHours  `json:"business_hours" gorm:"type:jsonb"`
	Timezone      string         `json:"timezone" gorm:"type:varchar(50);default:'UTC'"`

	// Status
	Status   string `json:"status" gorm:"type:varchar(20);default:'active'"`
	IsActive bool   `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop Shop `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
}

// SocialMediaLinks represents social media links
type SocialMediaLinks struct {
	Facebook  string `json:"facebook"`
	Instagram string `json:"instagram"`
	Twitter   string `json:"twitter"`
	Website   string `json:"website"`
	YouTube   string `json:"youtube"`
	TikTok    string `json:"tiktok"`
}

// ShopSettings represents shop-level settings
type ShopSettings struct {
	Currency             string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
}

// BranchSettings represents branch-level settings
type BranchSettings struct {
	Currency             string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
}

// BusinessHours represents business hours for each day
type BusinessHours map[string]string

// TableName returns the table name for Shop model
func (Shop) TableName() string {
	return "shops"
}

// TableName returns the table name for ShopBranch model
func (ShopBranch) TableName() string {
	return "shop_branches"
}

// Implement Scanner and Valuer interfaces for JSONB fields

// Value implements the driver.Valuer interface for SocialMediaLinks
func (s SocialMediaLinks) Value() (driver.Value, error) {
	if s == (SocialMediaLinks{}) {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for SocialMediaLinks
func (s *SocialMediaLinks) Scan(value interface{}) error {
	if value == nil {
		*s = SocialMediaLinks{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into SocialMediaLinks", value)
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for ShopSettings
func (s ShopSettings) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for ShopSettings
func (s *ShopSettings) Scan(value interface{}) error {
	if value == nil {
		*s = ShopSettings{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ShopSettings", value)
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for BranchSettings
func (s BranchSettings) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for BranchSettings
func (s *BranchSettings) Scan(value interface{}) error {
	if value == nil {
		*s = BranchSettings{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into BranchSettings", value)
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for BusinessHours
func (b BusinessHours) Value() (driver.Value, error) {
	if b == nil {
		return nil, nil
	}
	return json.Marshal(b)
}

// Scan implements the sql.Scanner interface for BusinessHours
func (b *BusinessHours) Scan(value interface{}) error {
	if value == nil {
		*b = BusinessHours{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into BusinessHours", value)
	}

	return json.Unmarshal(bytes, b)
}
