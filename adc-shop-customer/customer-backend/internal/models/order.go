package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Order represents a customer order
type Order struct {
	ID       uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ShopID   uuid.UUID      `json:"shop_id" gorm:"type:uuid;not null;index"`
	BranchID uuid.UUID      `json:"branch_id" gorm:"type:uuid;not null;index"`
	TableID  *uuid.UUID     `json:"table_id,omitempty" gorm:"type:uuid;index"` // For table orders
	
	// Customer information
	CustomerName  string  `json:"customer_name" gorm:"size:255;not null"`
	CustomerPhone string  `json:"customer_phone" gorm:"size:50;not null"`
	CustomerEmail *string `json:"customer_email,omitempty" gorm:"size:255"`
	
	// Order details
	OrderNumber      string           `json:"order_number" gorm:"size:50;uniqueIndex;not null"`
	OrderType        string           `json:"order_type" gorm:"size:50;not null"` // table_order, takeaway, delivery
	Status           string           `json:"status" gorm:"size:50;default:'pending'"` // pending, confirmed, preparing, ready, completed, cancelled
	Items            []OrderItem      `json:"items" gorm:"type:jsonb"`
	SpecialRequests  *string          `json:"special_requests,omitempty" gorm:"type:text"`
	
	// Pricing
	Subtotal       float64 `json:"subtotal" gorm:"type:decimal(10,2);not null"`
	ServiceCharge  float64 `json:"service_charge" gorm:"type:decimal(10,2);default:0"`
	Tax            float64 `json:"tax" gorm:"type:decimal(10,2);default:0"`
	Discount       float64 `json:"discount" gorm:"type:decimal(10,2);default:0"`
	Total          float64 `json:"total" gorm:"type:decimal(10,2);not null"`
	
	// Timestamps
	OrderedAt    time.Time  `json:"ordered_at" gorm:"not null"`
	ConfirmedAt  *time.Time `json:"confirmed_at,omitempty"`
	ReadyAt      *time.Time `json:"ready_at,omitempty"`
	CompletedAt  *time.Time `json:"completed_at,omitempty"`
	CancelledAt  *time.Time `json:"cancelled_at,omitempty"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Shop   *Shop       `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Table  *Table      `json:"table,omitempty" gorm:"foreignKey:TableID"`
}

// OrderItem represents an item in an order
type OrderItem struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description,omitempty"`
	Price       float64 `json:"price"`
	Quantity    int     `json:"quantity"`
	Subtotal    float64 `json:"subtotal"`
	ImageURL    string  `json:"image_url,omitempty"`
	
	// Menu item reference
	MenuItemID *string `json:"menu_item_id,omitempty"`
	
	// Customizations
	Customizations []OrderItemCustomization `json:"customizations,omitempty"`
	SpecialNotes   string                   `json:"special_notes,omitempty"`
}

// OrderItemCustomization represents customizations for an order item
type OrderItemCustomization struct {
	Name  string  `json:"name"`
	Value string  `json:"value"`
	Price float64 `json:"price,omitempty"`
}

// TableName returns the table name for Order model
func (Order) TableName() string {
	return "orders"
}

// BeforeCreate sets the ID and order number if not set
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}
	
	if o.OrderNumber == "" {
		// Generate order number: ORD-YYYYMMDD-HHMMSS-XXXX
		timestamp := time.Now().Format("20060102-150405")
		suffix := o.ID.String()[:4]
		o.OrderNumber = fmt.Sprintf("ORD-%s-%s", timestamp, suffix)
	}
	
	if o.OrderedAt.IsZero() {
		o.OrderedAt = time.Now()
	}
	
	return nil
}

// IsTableOrder checks if this is a table order
func (o *Order) IsTableOrder() bool {
	return o.TableID != nil && o.OrderType == "table_order"
}

// CanBeCancelled checks if the order can be cancelled
func (o *Order) CanBeCancelled() bool {
	return o.Status == "pending" || o.Status == "confirmed"
}

// CalculateTotal calculates the total amount for the order
func (o *Order) CalculateTotal() {
	o.Total = o.Subtotal + o.ServiceCharge + o.Tax - o.Discount
}

// Value implements the driver.Valuer interface for OrderItem slice
func (items []OrderItem) Value() (driver.Value, error) {
	if items == nil {
		return nil, nil
	}
	return json.Marshal(items)
}

// Scan implements the sql.Scanner interface for OrderItem slice
func (items *[]OrderItem) Scan(value interface{}) error {
	if value == nil {
		*items = []OrderItem{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into []OrderItem", value)
	}

	return json.Unmarshal(bytes, items)
}
