package repositories

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type OrderRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

func NewOrderRepository(db *gorm.DB, logger *logger.Logger) *OrderRepository {
	return &OrderRepository{
		db:     db,
		logger: logger,
	}
}

// CreateOrder creates a new order
func (r *OrderRepository) CreateOrder(ctx context.Context, order *models.Order) error {
	err := r.db.WithContext(ctx).Create(order).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to create order")
		return err
	}
	
	return nil
}

// GetOrderByID retrieves an order by its ID
func (r *OrderRepository) GetOrderByID(ctx context.Context, orderID uuid.UUID) (*models.Order, error) {
	var order models.Order
	
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("Table").
		Where("id = ?", orderID).
		First(&order).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get order by ID")
		return nil, err
	}
	
	return &order, nil
}

// GetOrderByNumber retrieves an order by its order number
func (r *OrderRepository) GetOrderByNumber(ctx context.Context, orderNumber string) (*models.Order, error) {
	var order models.Order
	
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("Table").
		Where("order_number = ?", orderNumber).
		First(&order).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get order by number")
		return nil, err
	}
	
	return &order, nil
}

// GetOrdersByTable retrieves orders for a specific table
func (r *OrderRepository) GetOrdersByTable(ctx context.Context, tableID uuid.UUID, limit int) ([]models.Order, error) {
	var orders []models.Order
	
	query := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Where("table_id = ?", tableID).
		Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&orders).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get orders by table")
		return nil, err
	}
	
	return orders, nil
}

// GetOrdersByBranch retrieves orders for a specific branch
func (r *OrderRepository) GetOrdersByBranch(ctx context.Context, branchID uuid.UUID, limit int, offset int) ([]models.Order, error) {
	var orders []models.Order
	
	query := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Table").
		Where("branch_id = ?", branchID).
		Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.Find(&orders).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get orders by branch")
		return nil, err
	}
	
	return orders, nil
}

// GetOrdersByCustomer retrieves orders for a specific customer (by phone)
func (r *OrderRepository) GetOrdersByCustomer(ctx context.Context, customerPhone string, limit int) ([]models.Order, error) {
	var orders []models.Order
	
	query := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("Table").
		Where("customer_phone = ?", customerPhone).
		Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&orders).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get orders by customer")
		return nil, err
	}
	
	return orders, nil
}

// UpdateOrderStatus updates the status of an order
func (r *OrderRepository) UpdateOrderStatus(ctx context.Context, orderID uuid.UUID, status string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	
	// Set timestamp fields based on status
	switch status {
	case "confirmed":
		updates["confirmed_at"] = time.Now()
	case "ready":
		updates["ready_at"] = time.Now()
	case "completed":
		updates["completed_at"] = time.Now()
	case "cancelled":
		updates["cancelled_at"] = time.Now()
	}
	
	err := r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("id = ?", orderID).
		Updates(updates).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to update order status")
		return err
	}
	
	return nil
}

// GetActiveOrdersByTable retrieves active orders for a table
func (r *OrderRepository) GetActiveOrdersByTable(ctx context.Context, tableID uuid.UUID) ([]models.Order, error) {
	var orders []models.Order
	
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Where("table_id = ? AND status IN (?)", tableID, []string{"pending", "confirmed", "preparing", "ready"}).
		Order("created_at DESC").
		Find(&orders).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to get active orders by table")
		return nil, err
	}
	
	return orders, nil
}

// CountOrdersByBranch counts total orders for a branch
func (r *OrderRepository) CountOrdersByBranch(ctx context.Context, branchID uuid.UUID) (int64, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("branch_id = ?", branchID).
		Count(&count).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to count orders by branch")
		return 0, err
	}
	
	return count, nil
}

// GetOrderStats retrieves order statistics for a branch
func (r *OrderRepository) GetOrderStats(ctx context.Context, branchID uuid.UUID, from, to time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Total orders
	var totalOrders int64
	err := r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("branch_id = ? AND created_at BETWEEN ? AND ?", branchID, from, to).
		Count(&totalOrders).Error
	if err != nil {
		return nil, err
	}
	stats["total_orders"] = totalOrders
	
	// Total revenue
	var totalRevenue float64
	err = r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("branch_id = ? AND status = ? AND created_at BETWEEN ? AND ?", branchID, "completed", from, to).
		Select("COALESCE(SUM(total), 0)").
		Scan(&totalRevenue).Error
	if err != nil {
		return nil, err
	}
	stats["total_revenue"] = totalRevenue
	
	// Orders by status
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&models.Order{}).
		Select("status, COUNT(*) as count").
		Where("branch_id = ? AND created_at BETWEEN ? AND ?", branchID, from, to).
		Group("status").
		Scan(&statusCounts).Error
	if err != nil {
		return nil, err
	}
	stats["orders_by_status"] = statusCounts
	
	return stats, nil
}
