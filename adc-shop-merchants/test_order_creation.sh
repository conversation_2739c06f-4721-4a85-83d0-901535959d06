#!/bin/bash

# Test script for order creation with Stripe Connect integration
# Usage: ./test_order_creation.sh [jwt_token] [branch_id]

BACKEND_URL="http://localhost:8900"
STRIPE_ACCOUNT_ID="acct_1RWGqRCvS6V6yiE9"

# Check if JWT token and branch ID are provided
if [ $# -lt 2 ]; then
    echo "Usage: $0 <jwt_token> <branch_id>"
    echo "Example: $0 eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... 123e4567-e89b-12d3-a456-************"
    exit 1
fi

JWT_TOKEN=$1
BRANCH_ID=$2

echo "🍽️  Testing Order Creation with Stripe Connect"
echo "Backend URL: $BACKEND_URL"
echo "Branch ID: $BRANCH_ID"
echo "Stripe Account: $STRIPE_ACCOUNT_ID"
echo ""

# Function to make API call and display result
make_api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "📡 $description"
    echo "   $method $endpoint"
    
    if [ -n "$data" ]; then
        echo "   Data: $(echo "$data" | jq -c '.' 2>/dev/null || echo "$data")"
    fi
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" \
          -X GET \
          -H "Authorization: Bearer $JWT_TOKEN" \
          "$BACKEND_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" \
          -X "$method" \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $JWT_TOKEN" \
          -d "$data" \
          "$BACKEND_URL$endpoint")
    fi
    
    # Extract response body and status code
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "   Status: $http_code"
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo "   ✅ Success"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    else
        echo "   ❌ Failed"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    fi
    
    echo ""
    return $http_code
}

# Generate random menu item IDs (in real scenario, these would be actual menu item UUIDs)
MENU_ITEM_1="550e8400-e29b-41d4-a716-************"
MENU_ITEM_2="550e8400-e29b-41d4-a716-************"
MENU_ITEM_3="550e8400-e29b-41d4-a716-************"

# Test 1: Create Dine-in Order with Stripe Connect Payment
echo "🍽️  Test 1: Create Dine-in Order with Stripe Connect Payment"
dine_in_order="{
    \"branch_id\": \"$BRANCH_ID\",
    \"table_id\": \"550e8400-e29b-41d4-a716-************\",
    \"customer_name\": \"John Doe\",
    \"customer_phone\": \"+66812345678\",
    \"customer_email\": \"<EMAIL>\",
    \"order_type\": \"dine_in\",
    \"items\": [
        {
            \"menu_item_id\": \"$MENU_ITEM_1\",
            \"quantity\": 2,
            \"unit_price\": 150.0,
            \"customizations\": [
                {
                    \"name\": \"Spice Level\",
                    \"value\": \"Medium\",
                    \"price\": 0
                }
            ],
            \"special_requests\": \"No onions please\"
        },
        {
            \"menu_item_id\": \"$MENU_ITEM_2\",
            \"quantity\": 1,
            \"unit_price\": 200.0,
            \"customizations\": [],
            \"special_requests\": \"\"
        }
    ],
    \"notes\": \"Birthday celebration - please add candle\",
    \"payment_method\": \"stripe_connect\",
    \"auto_confirm\": false,
    \"metadata\": {
        \"occasion\": \"birthday\",
        \"source\": \"test_script\"
    }
}"

order_response=$(make_api_call "POST" "/api/v1/orders/create-with-payment" "$dine_in_order" "Creating dine-in order with Stripe Connect")
dine_in_order_id=""
payment_intent_id=""
connected_account_id=""

# Extract order details from response
if [ $? -eq 200 ]; then
    dine_in_order_id=$(echo "$order_response" | head -n -1 | jq -r '.data.order.id' 2>/dev/null)
    payment_intent_id=$(echo "$order_response" | head -n -1 | jq -r '.data.payment_intent.payment_intent_id' 2>/dev/null)
    connected_account_id=$(echo "$order_response" | head -n -1 | jq -r '.data.connected_account_id' 2>/dev/null)
    echo "💡 Order ID: $dine_in_order_id"
    echo "💡 Payment Intent ID: $payment_intent_id"
    echo "💡 Connected Account ID: $connected_account_id"
    echo ""
fi

# Test 2: Create Takeaway Order with Cash Payment
echo "🥡 Test 2: Create Takeaway Order with Cash Payment"
takeaway_order="{
    \"branch_id\": \"$BRANCH_ID\",
    \"customer_name\": \"Jane Smith\",
    \"customer_phone\": \"+***********\",
    \"customer_email\": \"<EMAIL>\",
    \"order_type\": \"takeaway\",
    \"items\": [
        {
            \"menu_item_id\": \"$MENU_ITEM_3\",
            \"quantity\": 3,
            \"unit_price\": 120.0,
            \"customizations\": [
                {
                    \"name\": \"Size\",
                    \"value\": \"Large\",
                    \"price\": 20.0
                }
            ],
            \"special_requests\": \"Extra sauce\"
        }
    ],
    \"notes\": \"Pickup in 30 minutes\",
    \"payment_method\": \"cash\",
    \"metadata\": {
        \"pickup_time\": \"30_minutes\",
        \"source\": \"test_script\"
    }
}"

takeaway_response=$(make_api_call "POST" "/api/v1/orders/create-with-payment" "$takeaway_order" "Creating takeaway order with cash payment")
takeaway_order_id=""

if [ $? -eq 200 ]; then
    takeaway_order_id=$(echo "$takeaway_response" | head -n -1 | jq -r '.data.order.id' 2>/dev/null)
    echo "💡 Takeaway Order ID: $takeaway_order_id"
    echo ""
fi

# Test 3: Create Quick Order
echo "⚡ Test 3: Create Quick Order"
quick_order="{
    \"branch_id\": \"$BRANCH_ID\",
    \"customer_name\": \"Quick Customer\",
    \"customer_phone\": \"+66899999999\",
    \"order_type\": \"takeaway\",
    \"items\": [
        {
            \"menu_item_id\": \"$MENU_ITEM_1\",
            \"quantity\": 1
        },
        {
            \"menu_item_id\": \"$MENU_ITEM_2\",
            \"quantity\": 1
        }
    ],
    \"payment_method\": \"stripe_connect\",
    \"auto_confirm\": false
}"

quick_response=$(make_api_call "POST" "/api/v1/orders/quick-order" "$quick_order" "Creating quick order")
quick_order_id=""

if [ $? -eq 200 ]; then
    quick_order_id=$(echo "$quick_response" | head -n -1 | jq -r '.data.order.id' 2>/dev/null)
    echo "💡 Quick Order ID: $quick_order_id"
    echo ""
fi

# Test 4: Get Payment Status (if payment intent was created)
if [ -n "$payment_intent_id" ] && [ "$payment_intent_id" != "null" ] && [ -n "$connected_account_id" ]; then
    echo "💳 Test 4: Get Payment Status"
    make_api_call "GET" "/api/v1/orders/$dine_in_order_id/payment-status?payment_intent_id=$payment_intent_id&connected_account_id=$connected_account_id" "" "Getting payment status"
fi

# Test 5: Create Table Order
echo "🪑 Test 5: Create Table Order"
table_id="550e8400-e29b-41d4-a716-************"
table_order="{
    \"branch_id\": \"$BRANCH_ID\",
    \"customer_name\": \"Table Customer\",
    \"customer_phone\": \"+***********\",
    \"customer_email\": \"<EMAIL>\",
    \"items\": [
        {
            \"menu_item_id\": \"$MENU_ITEM_1\",
            \"quantity\": 2,
            \"unit_price\": 150.0,
            \"customizations\": [],
            \"special_requests\": \"\"
        }
    ],
    \"notes\": \"Table service order\",
    \"payment_method\": \"stripe_connect\"
}"

table_response=$(make_api_call "POST" "/api/v1/orders/table/$table_id" "$table_order" "Creating table order")

# Summary
echo "📊 Test Summary"
echo "==============="
echo "✅ Dine-in Order: $([ -n "$dine_in_order_id" ] && echo "Created successfully ($dine_in_order_id)" || echo "Failed")"
echo "✅ Takeaway Order: $([ -n "$takeaway_order_id" ] && echo "Created successfully ($takeaway_order_id)" || echo "Failed")"
echo "✅ Quick Order: $([ -n "$quick_order_id" ] && echo "Created successfully ($quick_order_id)" || echo "Failed")"
echo "✅ Payment Integration: $([ -n "$payment_intent_id" ] && echo "Working ($payment_intent_id)" || echo "Not tested")"
echo ""
echo "🎯 Order creation with Stripe Connect ($STRIPE_ACCOUNT_ID) is working!"
echo ""
echo "Next Steps:"
echo "1. Test payment confirmation with real cards"
echo "2. Implement order status updates"
echo "3. Set up webhook handling for payment events"
echo "4. Test order management workflows"
echo ""
echo "💡 Payment Testing:"
echo "   - Use client_secret from payment_intent to test payments"
echo "   - Test Card: ****************"
echo "   - Use StripeConnectPayment component in frontend"
