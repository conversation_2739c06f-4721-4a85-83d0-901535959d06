package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/utils"
)

// StripeConnectHandler handles Stripe Connect API endpoints
type StripeConnectHandler struct {
	stripeConnectService *services.StripeConnectService
	stripePaymentService *services.StripePaymentService
	logger               services.Logger
}

// NewStripeConnectHandler creates a new Stripe Connect handler
func NewStripeConnectHandler(stripeConnectService *services.StripeConnectService, stripePaymentService *services.StripePaymentService, logger services.Logger) *StripeConnectHandler {
	return &StripeConnectHandler{
		stripeConnectService: stripeConnectService,
		stripePaymentService: stripePaymentService,
		logger:               logger,
	}
}

// CreateConnectAccountRequest represents the request to create a Stripe Connect account
type CreateConnectAccountRequest struct {
	Email    string     `json:"email" binding:"required,email"`
	Country  string     `json:"country" binding:"required,len=2"`
	BranchID *uuid.UUID `json:"branch_id,omitempty"`
}

// RegisterExistingAccountRequest represents the request to register an existing Stripe Connect account
type RegisterExistingAccountRequest struct {
	StripeAccountID string     `json:"stripe_account_id" binding:"required"`
	BranchID        *uuid.UUID `json:"branch_id,omitempty"`
}

// CreateAccountSessionRequest represents the request to create an account session
type CreateAccountSessionRequest struct {
	Account string `json:"account" binding:"required"`
}

// ConnectAccountResponse represents the response for Stripe Connect account operations
type ConnectAccountResponse struct {
	AccountID        string `json:"account_id"`
	Email            string `json:"email"`
	Country          string `json:"country"`
	Type             string `json:"type"`
	Created          int64  `json:"created"`
	DetailsSubmitted bool   `json:"details_submitted"`
	ChargesEnabled   bool   `json:"charges_enabled"`
	PayoutsEnabled   bool   `json:"payouts_enabled"`
	AccountStatus    string `json:"account_status"`
	Requirements     *struct {
		CurrentlyDue        []string `json:"currently_due"`
		EventuallyDue       []string `json:"eventually_due"`
		PastDue             []string `json:"past_due"`
		PendingVerification []string `json:"pending_verification"`
	} `json:"requirements,omitempty"`
	Capabilities *struct {
		CardPayments string `json:"card_payments"`
		Transfers    string `json:"transfers"`
	} `json:"capabilities,omitempty"`
	BusinessProfile *struct {
		Name         string `json:"name,omitempty"`
		URL          string `json:"url,omitempty"`
		SupportEmail string `json:"support_email,omitempty"`
		SupportPhone string `json:"support_phone,omitempty"`
	} `json:"business_profile,omitempty"`
}

// CreateConnectAccount creates a new Stripe Connect account
func (h *StripeConnectHandler) CreateConnectAccount(c *gin.Context) {
	var req CreateConnectAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Get shop ID from context (set by auth middleware)
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Create Stripe Connect account
	account, err := h.stripeConnectService.CreateConnectAccount(c.Request.Context(), shopUUID, req.BranchID, req.Email, req.Country)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create Stripe Connect account")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create account", err.Error())
		return
	}

	// Convert to response format
	response := h.convertToResponse(account)

	utils.SuccessResponse(c, "Stripe Connect account created successfully", response)
}

// RegisterExistingAccount registers an existing Stripe Connect account
func (h *StripeConnectHandler) RegisterExistingAccount(c *gin.Context) {
	var req RegisterExistingAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Get shop ID from context (set by auth middleware)
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Register existing Stripe Connect account
	account, err := h.stripeConnectService.RegisterExistingAccount(c.Request.Context(), shopUUID, req.BranchID, req.StripeAccountID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to register existing Stripe Connect account")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to register account", err.Error())
		return
	}

	// Convert to response format
	response := h.convertToResponse(account)

	utils.SuccessResponse(c, "Existing Stripe Connect account registered successfully", response)
}

// GetConnectAccount retrieves Stripe Connect account details
func (h *StripeConnectHandler) GetConnectAccount(c *gin.Context) {
	// Get shop ID from context
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Get branch ID from query parameter (optional)
	var branchID *uuid.UUID
	if branchIDStr := c.Query("branch_id"); branchIDStr != "" {
		branchUUID, err := uuid.Parse(branchIDStr)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid branch ID format", err.Error())
			return
		}
		branchID = &branchUUID
	}

	// Get account by Stripe account ID if provided
	if accountID := c.Query("account_id"); accountID != "" {
		account, err := h.stripeConnectService.GetConnectAccountByStripeID(c.Request.Context(), accountID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusNotFound, "Account not found", err.Error())
			return
		}

		// Sync with Stripe to get latest data
		syncedAccount, err := h.stripeConnectService.SyncConnectAccount(c.Request.Context(), accountID)
		if err != nil {
			h.logger.WithError(err).Warn("Failed to sync account, returning cached data")
			syncedAccount = account
		}

		response := h.convertToResponse(syncedAccount)
		utils.SuccessResponse(c, "Account retrieved successfully", response)
		return
	}

	// Get account by shop/branch
	account, err := h.stripeConnectService.GetConnectAccount(c.Request.Context(), shopUUID, branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Account not found", err.Error())
		return
	}

	// Sync with Stripe to get latest data
	syncedAccount, err := h.stripeConnectService.SyncConnectAccount(c.Request.Context(), account.StripeAccountID)
	if err != nil {
		h.logger.WithError(err).Warn("Failed to sync account, returning cached data")
		syncedAccount = account
	}

	response := h.convertToResponse(syncedAccount)
	utils.SuccessResponse(c, "Account retrieved successfully", response)
}

// CreateAccountSession creates an account session for embedded components
func (h *StripeConnectHandler) CreateAccountSession(c *gin.Context) {
	var req CreateAccountSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	clientSecret, err := h.stripeConnectService.CreateAccountSession(c.Request.Context(), req.Account)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create account session")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create account session", err.Error())
		return
	}

	utils.SuccessResponse(c, "Account session created successfully", gin.H{
		"client_secret": clientSecret,
	})
}

// DeleteConnectAccount deletes/deactivates a Stripe Connect account
func (h *StripeConnectHandler) DeleteConnectAccount(c *gin.Context) {
	// Get shop ID from context
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Get branch ID from request body (optional)
	var req struct {
		BranchID *uuid.UUID `json:"branch_id,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	err := h.stripeConnectService.DeleteConnectAccount(c.Request.Context(), shopUUID, req.BranchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete Stripe Connect account")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete account", err.Error())
		return
	}

	utils.SuccessResponse(c, "Account deleted successfully", gin.H{
		"deleted": true,
	})
}

// ListConnectAccounts lists all Stripe Connect accounts for a shop
func (h *StripeConnectHandler) ListConnectAccounts(c *gin.Context) {
	// Get shop ID from context
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Get pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	accounts, err := h.stripeConnectService.ListConnectAccounts(c.Request.Context(), shopUUID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list Stripe Connect accounts")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to list accounts", err.Error())
		return
	}

	// Convert to response format
	responses := make([]ConnectAccountResponse, len(accounts))
	for i, account := range accounts {
		responses[i] = h.convertToResponse(&account)
	}

	utils.PaginatedResponse(c, "Accounts retrieved successfully", responses, page, limit, len(responses))
}

// convertToResponse converts a StripeConnectAccount model to response format
func (h *StripeConnectHandler) convertToResponse(account *models.StripeConnectAccount) ConnectAccountResponse {
	response := ConnectAccountResponse{
		AccountID:        account.StripeAccountID,
		Email:            account.Email,
		Country:          account.Country,
		Type:             account.AccountType,
		DetailsSubmitted: account.DetailsSubmitted,
		ChargesEnabled:   account.ChargesEnabled,
		PayoutsEnabled:   account.PayoutsEnabled,
		AccountStatus:    account.AccountStatus,
	}

	if account.StripeCreatedAt != nil {
		response.Created = account.StripeCreatedAt.Unix()
	}

	// Add requirements if any
	if len(account.CurrentlyDue) > 0 || len(account.EventuallyDue) > 0 || len(account.PastDue) > 0 || len(account.PendingVerification) > 0 {
		response.Requirements = &struct {
			CurrentlyDue        []string `json:"currently_due"`
			EventuallyDue       []string `json:"eventually_due"`
			PastDue             []string `json:"past_due"`
			PendingVerification []string `json:"pending_verification"`
		}{
			CurrentlyDue:        account.CurrentlyDue,
			EventuallyDue:       account.EventuallyDue,
			PastDue:             account.PastDue,
			PendingVerification: account.PendingVerification,
		}
	}

	// Add capabilities
	response.Capabilities = &struct {
		CardPayments string `json:"card_payments"`
		Transfers    string `json:"transfers"`
	}{
		CardPayments: account.CardPayments,
		Transfers:    account.Transfers,
	}

	// Add business profile if available
	if account.BusinessName != "" || account.BusinessURL != "" || account.SupportEmail != "" || account.SupportPhone != "" {
		response.BusinessProfile = &struct {
			Name         string `json:"name,omitempty"`
			URL          string `json:"url,omitempty"`
			SupportEmail string `json:"support_email,omitempty"`
			SupportPhone string `json:"support_phone,omitempty"`
		}{
			Name:         account.BusinessName,
			URL:          account.BusinessURL,
			SupportEmail: account.SupportEmail,
			SupportPhone: account.SupportPhone,
		}
	}

	return response
}

// CreatePaymentIntent creates a payment intent for the connected account
func (h *StripeConnectHandler) CreatePaymentIntent(c *gin.Context) {
	var req services.CreatePaymentIntentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Get shop ID from context (set by auth middleware)
	shopID, exists := c.Get("shop_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Shop ID not found", "")
		return
	}

	shopUUID, ok := shopID.(uuid.UUID)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Invalid shop ID format", "")
		return
	}

	// Set shop ID from auth context
	req.ShopID = shopUUID

	// Create payment intent
	response, err := h.stripePaymentService.CreatePaymentIntent(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create payment intent", err.Error())
		return
	}

	utils.SuccessResponse(c, "Payment intent created successfully", response)
}

// GetPaymentIntent retrieves a payment intent
func (h *StripeConnectHandler) GetPaymentIntent(c *gin.Context) {
	paymentIntentID := c.Param("payment_intent_id")
	if paymentIntentID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Payment intent ID is required", "")
		return
	}

	connectedAccountID := c.Query("connected_account_id")
	if connectedAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Connected account ID is required", "")
		return
	}

	response, err := h.stripePaymentService.GetPaymentIntent(c.Request.Context(), paymentIntentID, connectedAccountID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get payment intent", err.Error())
		return
	}

	utils.SuccessResponse(c, "Payment intent retrieved successfully", response)
}

// ConfirmPaymentIntent confirms a payment intent
func (h *StripeConnectHandler) ConfirmPaymentIntent(c *gin.Context) {
	paymentIntentID := c.Param("payment_intent_id")
	if paymentIntentID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Payment intent ID is required", "")
		return
	}

	var req struct {
		ConnectedAccountID string  `json:"connected_account_id" binding:"required"`
		PaymentMethodID    *string `json:"payment_method_id,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	response, err := h.stripePaymentService.ConfirmPaymentIntent(c.Request.Context(), paymentIntentID, req.ConnectedAccountID, req.PaymentMethodID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to confirm payment intent", err.Error())
		return
	}

	utils.SuccessResponse(c, "Payment intent confirmed successfully", response)
}

// CancelPaymentIntent cancels a payment intent
func (h *StripeConnectHandler) CancelPaymentIntent(c *gin.Context) {
	paymentIntentID := c.Param("payment_intent_id")
	if paymentIntentID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Payment intent ID is required", "")
		return
	}

	var req struct {
		ConnectedAccountID string  `json:"connected_account_id" binding:"required"`
		Reason             *string `json:"reason,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	response, err := h.stripePaymentService.CancelPaymentIntent(c.Request.Context(), paymentIntentID, req.ConnectedAccountID, req.Reason)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to cancel payment intent", err.Error())
		return
	}

	utils.SuccessResponse(c, "Payment intent cancelled successfully", response)
}
