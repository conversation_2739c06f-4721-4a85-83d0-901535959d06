package routes

import (
	"github.com/gin-gonic/gin"
	"restaurant-backend/internal/handlers"
	"restaurant-backend/internal/middleware"
)

// SetupStripeConnectRoutes sets up Stripe Connect related routes
func SetupStripeConnectRoutes(router *gin.Engine, stripeConnectHandler *handlers.StripeConnectHandler, authMiddleware *middleware.AuthMiddleware) {
	// Stripe Connect routes (protected)
	stripeGroup := router.Group("/api/v1/stripe")
	stripeGroup.Use(authMiddleware.RequireAuth())
	{
		// Connect account management
		stripeGroup.POST("/connect", stripeConnectHandler.CreateConnectAccount)
		stripeGroup.GET("/connect", stripeConnectHandler.GetConnectAccount)
		stripeGroup.DELETE("/connect", stripeConnectHandler.DeleteConnectAccount)
		stripeGroup.GET("/connect/list", stripeConnectHandler.ListConnectAccounts)

		// Account session for embedded components
		stripeGroup.POST("/account_session", stripeConnectHandler.CreateAccountSession)
	}
}
