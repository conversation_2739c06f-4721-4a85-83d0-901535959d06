package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v78"
	"github.com/stripe/stripe-go/v78/account"
	"github.com/stripe/stripe-go/v78/accountsession"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
)

// StripeConnectService handles Stripe Connect operations
type StripeConnectService struct {
	db     *gorm.DB
	logger Logger
}

// NewStripeConnectService creates a new Stripe Connect service
func NewStripeConnectService(db *gorm.DB, logger Logger) *StripeConnectService {
	return &StripeConnectService{
		db:     db,
		logger: logger,
	}
}

// CreateConnectAccount creates a new Stripe Connect account
func (s *StripeConnectService) CreateConnectAccount(ctx context.Context, shopID uuid.UUID, branchID *uuid.UUID, email, country string) (*models.StripeConnectAccount, error) {
	// Check if account already exists for this shop/branch
	var existingAccount models.StripeConnectAccount
	query := s.db.Where("shop_id = ?", shopID)
	if branchID != nil {
		query = query.Where("branch_id = ?", *branchID)
	} else {
		query = query.Where("branch_id IS NULL")
	}
	
	if err := query.First(&existingAccount).Error; err == nil {
		return nil, fmt.Errorf("stripe connect account already exists for this shop/branch")
	}

	// Create Stripe account
	params := &stripe.AccountParams{
		Type:    stripe.String("express"),
		Country: stripe.String(country),
		Email:   stripe.String(email),
		Capabilities: &stripe.AccountCapabilitiesParams{
			CardPayments: &stripe.AccountCapabilitiesCardPaymentsParams{
				Requested: stripe.Bool(true),
			},
			Transfers: &stripe.AccountCapabilitiesTransfersParams{
				Requested: stripe.Bool(true),
			},
		},
		BusinessType: stripe.String("company"),
		Settings: &stripe.AccountSettingsParams{
			Payouts: &stripe.AccountSettingsPayoutsParams{
				Schedule: &stripe.AccountSettingsPayoutsScheduleParams{
					Interval: stripe.String("daily"),
				},
			},
		},
	}

	stripeAccount, err := account.New(params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create Stripe Connect account")
		return nil, fmt.Errorf("failed to create stripe account: %w", err)
	}

	// Create local record
	connectAccount := &models.StripeConnectAccount{
		ShopID:          shopID,
		BranchID:        branchID,
		StripeAccountID: stripeAccount.ID,
		AccountType:     string(stripeAccount.Type),
		Country:         stripeAccount.Country,
		Email:           stripeAccount.Email,
		DefaultCurrency: stripeAccount.DefaultCurrency,
		DetailsSubmitted: stripeAccount.DetailsSubmitted,
		ChargesEnabled:  stripeAccount.ChargesEnabled,
		PayoutsEnabled:  stripeAccount.PayoutsEnabled,
	}

	// Convert Stripe account to map for UpdateFromStripeAccount
	accountData, _ := json.Marshal(stripeAccount)
	var accountMap map[string]interface{}
	json.Unmarshal(accountData, &accountMap)
	connectAccount.UpdateFromStripeAccount(accountMap)

	if stripeAccount.Created > 0 {
		createdAt := time.Unix(stripeAccount.Created, 0)
		connectAccount.StripeCreatedAt = &createdAt
	}

	if err := s.db.Create(connectAccount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to save Stripe Connect account")
		return nil, fmt.Errorf("failed to save account: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"shop_id":           shopID,
		"branch_id":         branchID,
		"stripe_account_id": stripeAccount.ID,
	}).Info("Stripe Connect account created successfully")

	return connectAccount, nil
}

// GetConnectAccount retrieves a Stripe Connect account
func (s *StripeConnectService) GetConnectAccount(ctx context.Context, shopID uuid.UUID, branchID *uuid.UUID) (*models.StripeConnectAccount, error) {
	var connectAccount models.StripeConnectAccount
	query := s.db.Where("shop_id = ? AND is_active = ?", shopID, true)
	if branchID != nil {
		query = query.Where("branch_id = ?", *branchID)
	} else {
		query = query.Where("branch_id IS NULL")
	}

	if err := query.First(&connectAccount).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("stripe connect account not found")
		}
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	return &connectAccount, nil
}

// GetConnectAccountByStripeID retrieves account by Stripe account ID
func (s *StripeConnectService) GetConnectAccountByStripeID(ctx context.Context, stripeAccountID string) (*models.StripeConnectAccount, error) {
	var connectAccount models.StripeConnectAccount
	if err := s.db.Where("stripe_account_id = ? AND is_active = ?", stripeAccountID, true).First(&connectAccount).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("stripe connect account not found")
		}
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	return &connectAccount, nil
}

// SyncConnectAccount syncs account data from Stripe
func (s *StripeConnectService) SyncConnectAccount(ctx context.Context, stripeAccountID string) (*models.StripeConnectAccount, error) {
	// Get account from Stripe
	stripeAccount, err := account.Get(stripeAccountID, nil)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get Stripe account")
		return nil, fmt.Errorf("failed to get stripe account: %w", err)
	}

	// Get local record
	connectAccount, err := s.GetConnectAccountByStripeID(ctx, stripeAccountID)
	if err != nil {
		return nil, err
	}

	// Update from Stripe data
	accountData, _ := json.Marshal(stripeAccount)
	var accountMap map[string]interface{}
	json.Unmarshal(accountData, &accountMap)
	connectAccount.UpdateFromStripeAccount(accountMap)

	// Save updates
	if err := s.db.Save(connectAccount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to update Stripe Connect account")
		return nil, fmt.Errorf("failed to update account: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"stripe_account_id": stripeAccountID,
		"account_status":    connectAccount.AccountStatus,
	}).Info("Stripe Connect account synced successfully")

	return connectAccount, nil
}

// CreateAccountSession creates an account session for embedded components
func (s *StripeConnectService) CreateAccountSession(ctx context.Context, stripeAccountID string) (string, error) {
	params := &stripe.AccountSessionParams{
		Account: stripe.String(stripeAccountID),
		Components: &stripe.AccountSessionComponentsParams{
			AccountOnboarding: &stripe.AccountSessionComponentsAccountOnboardingParams{
				Enabled: stripe.Bool(true),
			},
			Documents: &stripe.AccountSessionComponentsDocumentsParams{
				Enabled: stripe.Bool(true),
			},
			PaymentDetails: &stripe.AccountSessionComponentsPaymentDetailsParams{
				Enabled: stripe.Bool(true),
			},
			Payouts: &stripe.AccountSessionComponentsPayoutsParams{
				Enabled: stripe.Bool(true),
			},
			AccountManagement: &stripe.AccountSessionComponentsAccountManagementParams{
				Enabled: stripe.Bool(true),
			},
		},
	}

	session, err := accountsession.New(params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create account session")
		return "", fmt.Errorf("failed to create account session: %w", err)
	}

	return session.ClientSecret, nil
}

// DeleteConnectAccount deactivates a Stripe Connect account
func (s *StripeConnectService) DeleteConnectAccount(ctx context.Context, shopID uuid.UUID, branchID *uuid.UUID) error {
	// Get local account
	connectAccount, err := s.GetConnectAccount(ctx, shopID, branchID)
	if err != nil {
		return err
	}

	// Delete from Stripe
	_, err = account.Del(connectAccount.StripeAccountID, nil)
	if err != nil {
		s.logger.WithError(err).Error("Failed to delete Stripe account")
		return fmt.Errorf("failed to delete stripe account: %w", err)
	}

	// Deactivate local record
	connectAccount.IsActive = false
	connectAccount.AccountStatus = "inactive"
	if err := s.db.Save(connectAccount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to deactivate Stripe Connect account")
		return fmt.Errorf("failed to deactivate account: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"shop_id":           shopID,
		"branch_id":         branchID,
		"stripe_account_id": connectAccount.StripeAccountID,
	}).Info("Stripe Connect account deleted successfully")

	return nil
}

// ListConnectAccounts lists all active Stripe Connect accounts for a shop
func (s *StripeConnectService) ListConnectAccounts(ctx context.Context, shopID uuid.UUID) ([]models.StripeConnectAccount, error) {
	var accounts []models.StripeConnectAccount
	if err := s.db.Where("shop_id = ? AND is_active = ?", shopID, true).Find(&accounts).Error; err != nil {
		return nil, fmt.Errorf("failed to list accounts: %w", err)
	}

	return accounts, nil
}
